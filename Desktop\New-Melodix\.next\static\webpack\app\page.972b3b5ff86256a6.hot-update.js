"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/HomePage.tsx":
/*!*************************************!*\
  !*** ./app/components/HomePage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./app/components/Sidebar.tsx\");\n/* harmony import */ var _TopBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopBar */ \"(app-pages-browser)/./app/components/TopBar.tsx\");\n/* harmony import */ var _QuickPlayCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuickPlayCard */ \"(app-pages-browser)/./app/components/QuickPlayCard.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Card */ \"(app-pages-browser)/./app/components/Card.tsx\");\n/* harmony import */ var _SongCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SongCard */ \"(app-pages-browser)/./app/components/SongCard.tsx\");\n/* harmony import */ var _Player__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Player */ \"(app-pages-browser)/./app/components/Player.tsx\");\n/* harmony import */ var _layout_Providers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./layout/Providers */ \"(app-pages-browser)/./app/components/layout/Providers.tsx\");\n/* harmony import */ var _contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../contexts/PlayerContext */ \"(app-pages-browser)/./app/contexts/PlayerContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const [greeting, setGreeting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Good evening');\n    const [songs, setSongs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { setPlaylist } = (0,_contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__.usePlayer)();\n    // Update greeting based on time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const updateGreeting = {\n                \"HomePage.useEffect.updateGreeting\": ()=>{\n                    const hour = new Date().getHours();\n                    if (hour < 12) {\n                        setGreeting('Good morning');\n                    } else if (hour < 18) {\n                        setGreeting('Good afternoon');\n                    } else {\n                        setGreeting('Good evening');\n                    }\n                }\n            }[\"HomePage.useEffect.updateGreeting\"];\n            updateGreeting();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Fetch songs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchSongs = {\n                \"HomePage.useEffect.fetchSongs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/songs');\n                        const data = await response.json();\n                        if (response.ok) {\n                            setSongs(data.songs);\n                            setPlaylist(data.songs);\n                        } else {\n                            console.error('Failed to fetch songs:', data.error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching songs:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchSongs\"];\n            fetchSongs();\n        }\n    }[\"HomePage.useEffect\"], [\n        setPlaylist\n    ]);\n    const quickPlayData = [\n        {\n            title: 'Liked Songs',\n            color: '#e91e63'\n        },\n        {\n            title: 'Chill Vibes',\n            color: '#9c27b0'\n        },\n        {\n            title: 'Workout Mix',\n            color: '#f44336'\n        },\n        {\n            title: 'Focus Flow',\n            color: '#009688'\n        },\n        {\n            title: 'Party Hits',\n            color: '#ff9800'\n        },\n        {\n            title: 'Road Trip',\n            color: '#3f51b5'\n        }\n    ];\n    const madeForYouData = [\n        {\n            title: 'Daily Mix 1',\n            description: 'The Weeknd, Drake, Travis Scott and more',\n            color: '#4caf50'\n        },\n        {\n            title: 'Discover Weekly',\n            description: 'Your weekly mixtape of fresh music',\n            color: '#2196f3'\n        },\n        {\n            title: 'Release Radar',\n            description: 'Catch all the latest music from artists you follow',\n            color: '#ff5722'\n        },\n        {\n            title: 'Repeat Rewind',\n            description: 'Songs you can\\'t stop playing',\n            color: '#673ab7'\n        },\n        {\n            title: 'Summer Hits',\n            description: 'The hottest tracks for sunny days',\n            color: '#795548'\n        },\n        {\n            title: 'Acoustic Favorites',\n            description: 'Stripped down versions of your favorites',\n            color: '#607d8b'\n        }\n    ];\n    const popularPlaylistsData = [\n        {\n            title: 'Today\\'s Top Hits',\n            description: 'Ed Sheeran is on top of the Hottest 50!',\n            color: '#e91e63'\n        },\n        {\n            title: 'RapCaviar',\n            description: 'New music from Kendrick Lamar, Travis Scott and more',\n            color: '#009688'\n        },\n        {\n            title: 'Rock Classics',\n            description: 'Rock legends & epic songs',\n            color: '#ff9800'\n        },\n        {\n            title: 'Jazz Vibes',\n            description: 'The original chill instrumental music',\n            color: '#3f51b5'\n        },\n        {\n            title: 'Mint',\n            description: 'The best in electronic music',\n            color: '#f44336'\n        },\n        {\n            title: 'Pop Rising',\n            description: 'Rising pop stars and fresh favorites',\n            color: '#9c27b0'\n        }\n    ];\n    const handlePlayTrack = ()=>{\n        // This would be handled by a music player context/state in a real app\n        console.log('Playing track');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_Providers__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    activeItem: \"home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"main-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"content-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"greeting\",\n                                    children: greeting\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"quick-play-grid\",\n                                    children: quickPlayData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickPlayCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            title: item.title,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Made for you\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cards-grid\",\n                                    children: madeForYouData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            title: item.title,\n                                            description: item.description,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Recently added songs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"songs-list\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-400\",\n                                        children: \"Loading songs...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined) : songs.length > 0 ? songs.map((song)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SongCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            song: song\n                                        }, song._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-400\",\n                                        children: \"No songs available yet. Check back later!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Popular playlists\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cards-grid\",\n                                    children: popularPlaylistsData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            title: item.title,\n                                            description: item.description,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Player__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"a8bhbGXzpENZSGZ1gBO2QsreV/Y=\", false, function() {\n    return [\n        _contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__.usePlayer\n    ];\n});\n_c = HomePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/HomePage.tsx\n"));

/***/ })

});