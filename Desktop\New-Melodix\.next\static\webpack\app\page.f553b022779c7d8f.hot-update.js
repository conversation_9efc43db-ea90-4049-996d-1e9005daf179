"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-pages-browser)/./node_modules/@fortawesome/react-fontawesome/dist/index.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(app-pages-browser)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default,SearchBar auto */ \n\n\n\nconst LikedSongsCard = (param)=>{\n    let { count = 0, pinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"liked-card\",\n        href: \"/liked\",\n        \"aria-label\": \"Open Liked Songs playlist\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-art\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: \"heart-icon\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-text\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-title\",\n                        children: \"Liked Songs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-meta\",\n                        style: {\n                            textAlign: 'center',\n                            paddingRight: '10px'\n                        },\n                        children: [\n                            pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faThumbtack,\n                                style: {\n                                    color: '#1ed760',\n                                    transform: 'rotate(45deg)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 22\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Playlist\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined),\n                            typeof count === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"• \",\n                                    count,\n                                    \" songs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 41\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LikedSongsCard;\nconst Sidebar = (param)=>{\n    let { activeItem = 'home', likedCount = 0, likedPinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo\",\n                children: \"Melodix\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    className: \"nav-item \".concat(activeItem === 'home' ? 'active' : ''),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12.5 3.247a1 1 0 00-1 0L4 7.577V20h4.5v-6a1 1 0 011-1h5a1 1 0 011 1v6H20V7.577l-7.5-4.33z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divider\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"playlists\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LikedSongsCard, {\n                    count: likedCount,\n                    pinned: likedPinned\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Sidebar;\n// New SearchBar component (non-functional, visual only)\nconst SearchBar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            backgroundColor: '#121212',\n            borderRadius: '20px',\n            padding: '8px 16px',\n            color: '#b3b3b3',\n            fontSize: '14px',\n            width: '100%',\n            maxWidth: '400px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                style: {\n                    width: '20px',\n                    height: '20px',\n                    marginRight: '8px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M10.533 1.279c-5.18 0-9.407 4.14-9.407 9.279s4.226 9.279 9.407 9.279c2.234 0 4.29-.77 5.907-2.058l4.353 4.353a1 1 0 101.414-1.414l-4.344-4.344a9.157 9.157 0 002.077-5.816c0-5.14-4.226-9.28-9.407-9.28zm-7.407 9.279c0-4.006 3.302-7.28 7.407-7.28s7.407 3.274 7.407 7.28-3.302 7.279-7.407 7.279-7.407-3.273-7.407-7.28z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"What do you want to play?\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = SearchBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n // Export the new component so you can import it elsewhere\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"LikedSongsCard\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"SearchBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});