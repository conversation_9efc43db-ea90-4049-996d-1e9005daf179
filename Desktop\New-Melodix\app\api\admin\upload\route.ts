import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';
import formidable from 'formidable';
import { generateUniqueFileName, uploadFileToStorage } from '../../../lib/storage';

export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(request: NextRequest) {
  try {
    // Convert NextRequest to regular request for formidable
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const coverFile = formData.get('cover') as File;

    if (!file || !coverFile) {
      return NextResponse.json(
        { error: 'Both audio file and cover image are required' },
        { status: 400 }
      );
    }

    // Generate unique filenames
    const audioFileName = generateUniqueFileName(file.name, 'audio');
    const coverFileName = generateUniqueFileName(coverFile.name, 'covers');

    // Read file contents
    const audioFileBuffer = await file.arrayBuffer();
    const coverFileBuffer = await coverFile.arrayBuffer();

    // Upload to Bunny.net storage
    const audioUrl = await uploadFileToStorage(Buffer.from(audioFileBuffer), {
      fileName: audioFileName,
      contentType: file.type,
      folder: 'audio',
    });

    const coverUrl = await uploadFileToStorage(Buffer.from(coverFileBuffer), {
      fileName: coverFileName,
      contentType: coverFile.type,
      folder: 'covers',
    });

    return NextResponse.json({
      audioUrl,
      coverUrl,
      audioFileName,
      coverFileName,
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload files' },
      { status: 500 }
    );
  }
}
