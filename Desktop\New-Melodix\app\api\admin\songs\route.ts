import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../lib/auth';
import { connectToDatabase } from '../../../lib/db';
import Song from '../../../lib/models/Song';
import User from '../../../lib/models/User';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const { title, author, coverImageUrl, audioUrl } = await request.json();

    // Validate input
    if (!title || !author || !coverImageUrl || !audioUrl) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Find user
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create new song
    const newSong = new Song({
      title,
      author,
      coverImageUrl,
      audioUrl,
      uploadedBy: user._id,
    });

    // Save song to database
    await newSong.save();

    // Populate user information
    const populatedSong = await Song.findById(newSong._id)
      .populate('uploadedBy', 'firstName lastName username email');

    return NextResponse.json({
      message: 'Song added successfully',
      song: populatedSong,
    });

  } catch (error) {
    console.error('Add song error:', error);
    return NextResponse.json(
      { error: 'Failed to add song' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get songs with pagination
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get total count
    const totalSongs = await Song.countDocuments();

    // Get songs
    const songs = await Song.find()
      .populate('uploadedBy', 'firstName lastName username email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    return NextResponse.json({
      songs,
      pagination: {
        total: totalSongs,
        page,
        limit,
        totalPages: Math.ceil(totalSongs / limit),
      },
    });

  } catch (error) {
    console.error('Get songs error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch songs' },
      { status: 500 }
    );
  }
}