import mongoose from 'mongoose';

export interface ISong extends mongoose.Document {
  title: string;
  author: string;
  coverImageUrl: string;
  audioUrl: string;
  uploadedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const SongSchema = new mongoose.Schema<ISong>({
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  author: {
    type: String,
    required: [true, 'Author is required'],
    trim: true,
    maxlength: [50, 'Author cannot be more than 50 characters']
  },
  coverImageUrl: {
    type: String,
    required: [true, 'Cover image URL is required'],
    trim: true
  },
  audioUrl: {
    type: String,
    required: [true, 'Audio URL is required'],
    trim: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Uploaded by is required']
  }
}, {
  timestamps: true
});

const Song = mongoose.models.Song || mongoose.model<ISong>('Song', SongSchema);

export default Song;