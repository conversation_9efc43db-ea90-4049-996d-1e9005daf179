"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/mongodb-adapter/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@auth/mongodb-adapter/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MongoDBAdapter: () => (/* binding */ MongoDBAdapter),\n/* harmony export */   _id: () => (/* binding */ _id),\n/* harmony export */   defaultCollections: () => (/* binding */ defaultCollections),\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\nvar __addDisposableResource = (undefined && undefined.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (undefined && undefined.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  <p>Official <a href=\"https://www.mongodb.com\">MongoDB</a> adapter for Auth.js / NextAuth.js.</p>\n *  <a href=\"https://www.mongodb.com\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/mongodb.svg\" width=\"30\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @auth/mongodb-adapter mongodb\n * ```\n *\n * @module @auth/mongodb-adapter\n */\n\n/**\n * This adapter uses https://www.typescriptlang.org/docs/handbook/release-notes/typescript-5-2.html#using-declarations-and-explicit-resource-management.\n * This feature is very new and requires runtime polyfills for `Symbol.asyncDispose` in order to work properly in all environments.\n * It is also required to set in the `tsconfig.json` file the compilation target to `es2022` or below and configure the `lib` option to include `esnext` or `esnext.disposable`.\n *\n * You can find more information about this feature and the polyfills in the link above.\n */\n// @ts-expect-error read only property is not assignable\nSymbol.asyncDispose ?? (Symbol.asyncDispose = Symbol(\"Symbol.asyncDispose\"));\nconst defaultCollections = {\n    Users: \"users\",\n    Accounts: \"accounts\",\n    Sessions: \"sessions\",\n    VerificationTokens: \"verification_tokens\",\n};\nconst format = {\n    /** Takes a MongoDB object and returns a plain old JavaScript object */\n    from(object) {\n        const newObject = {};\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"_id\") {\n                newObject.id = value.toHexString();\n            }\n            else if (key === \"userId\") {\n                newObject[key] = value.toHexString();\n            }\n            else {\n                newObject[key] = value;\n            }\n        }\n        return newObject;\n    },\n    /** Takes a plain old JavaScript object and turns it into a MongoDB object */\n    to(object) {\n        const newObject = {\n            _id: _id(object.id),\n        };\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"userId\")\n                newObject[key] = _id(value);\n            else if (key === \"id\")\n                continue;\n            else\n                newObject[key] = value;\n        }\n        return newObject;\n    },\n};\n/** @internal */\nfunction _id(hex) {\n    if (hex?.length !== 24)\n        return new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId();\n    return new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(hex);\n}\nfunction MongoDBAdapter(\n/**\n * The MongoDB client.\n *\n * The MongoDB team recommends providing a non-connected `MongoClient` instance to avoid unhandled promise rejections if the client fails to connect.\n *\n * Alternatively, you can also pass:\n * - A promise that resolves to a connected `MongoClient` (not recommended).\n * - A function, to handle more complex and custom connection strategies.\n *\n * Using a function combined with `options.onClose`, can be useful when you want a more advanced and customized connection strategy to address challenges related to persistence, container reuse, and connection closure.\n */\nclient, options = {}) {\n    const { collections } = options;\n    const { from, to } = format;\n    const getDb = async () => {\n        const _client = await (typeof client === \"function\"\n            ? client()\n            : client);\n        const _db = _client.db(options.databaseName);\n        const c = { ...defaultCollections, ...collections };\n        return {\n            U: _db.collection(c.Users),\n            A: _db.collection(c.Accounts),\n            S: _db.collection(c.Sessions),\n            V: _db.collection(c?.VerificationTokens),\n            [Symbol.asyncDispose]: async () => {\n                await options.onClose?.(_client);\n            },\n        };\n    };\n    return {\n        async createUser(data) {\n            const env_1 = { stack: [], error: void 0, hasError: false };\n            try {\n                const user = to(data);\n                const db = __addDisposableResource(env_1, await getDb(), true);\n                await db.U.insertOne(user);\n                return from(user);\n            }\n            catch (e_1) {\n                env_1.error = e_1;\n                env_1.hasError = true;\n            }\n            finally {\n                const result_1 = __disposeResources(env_1);\n                if (result_1)\n                    await result_1;\n            }\n        },\n        async getUser(id) {\n            const env_2 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_2, await getDb(), true);\n                const user = await db.U.findOne({ _id: _id(id) });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_2) {\n                env_2.error = e_2;\n                env_2.hasError = true;\n            }\n            finally {\n                const result_2 = __disposeResources(env_2);\n                if (result_2)\n                    await result_2;\n            }\n        },\n        async getUserByEmail(email) {\n            const env_3 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_3, await getDb(), true);\n                const user = await db.U.findOne({ email });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_3) {\n                env_3.error = e_3;\n                env_3.hasError = true;\n            }\n            finally {\n                const result_3 = __disposeResources(env_3);\n                if (result_3)\n                    await result_3;\n            }\n        },\n        async getUserByAccount(provider_providerAccountId) {\n            const env_4 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_4, await getDb(), true);\n                const account = await db.A.findOne(provider_providerAccountId);\n                if (!account)\n                    return null;\n                const user = await db.U.findOne({ _id: new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(account.userId) });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_4) {\n                env_4.error = e_4;\n                env_4.hasError = true;\n            }\n            finally {\n                const result_4 = __disposeResources(env_4);\n                if (result_4)\n                    await result_4;\n            }\n        },\n        async updateUser(data) {\n            const env_5 = { stack: [], error: void 0, hasError: false };\n            try {\n                const { _id, ...user } = to(data);\n                const db = __addDisposableResource(env_5, await getDb(), true);\n                const result = await db.U.findOneAndUpdate({ _id }, { $set: user }, { returnDocument: \"after\" });\n                return from(result);\n            }\n            catch (e_5) {\n                env_5.error = e_5;\n                env_5.hasError = true;\n            }\n            finally {\n                const result_5 = __disposeResources(env_5);\n                if (result_5)\n                    await result_5;\n            }\n        },\n        async deleteUser(id) {\n            const env_6 = { stack: [], error: void 0, hasError: false };\n            try {\n                const userId = _id(id);\n                const db = __addDisposableResource(env_6, await getDb(), true);\n                await Promise.all([\n                    db.A.deleteMany({ userId: userId }),\n                    db.S.deleteMany({ userId: userId }),\n                    db.U.deleteOne({ _id: userId }),\n                ]);\n            }\n            catch (e_6) {\n                env_6.error = e_6;\n                env_6.hasError = true;\n            }\n            finally {\n                const result_6 = __disposeResources(env_6);\n                if (result_6)\n                    await result_6;\n            }\n        },\n        linkAccount: async (data) => {\n            const env_7 = { stack: [], error: void 0, hasError: false };\n            try {\n                const account = to(data);\n                const db = __addDisposableResource(env_7, await getDb(), true);\n                await db.A.insertOne(account);\n                return account;\n            }\n            catch (e_7) {\n                env_7.error = e_7;\n                env_7.hasError = true;\n            }\n            finally {\n                const result_7 = __disposeResources(env_7);\n                if (result_7)\n                    await result_7;\n            }\n        },\n        async unlinkAccount(provider_providerAccountId) {\n            const env_8 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_8, await getDb(), true);\n                const account = await db.A.findOneAndDelete(provider_providerAccountId);\n                return from(account);\n            }\n            catch (e_8) {\n                env_8.error = e_8;\n                env_8.hasError = true;\n            }\n            finally {\n                const result_8 = __disposeResources(env_8);\n                if (result_8)\n                    await result_8;\n            }\n        },\n        async getSessionAndUser(sessionToken) {\n            const env_9 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_9, await getDb(), true);\n                const session = await db.S.findOne({ sessionToken });\n                if (!session)\n                    return null;\n                const user = await db.U.findOne({ _id: new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(session.userId) });\n                if (!user)\n                    return null;\n                return {\n                    user: from(user),\n                    session: from(session),\n                };\n            }\n            catch (e_9) {\n                env_9.error = e_9;\n                env_9.hasError = true;\n            }\n            finally {\n                const result_9 = __disposeResources(env_9);\n                if (result_9)\n                    await result_9;\n            }\n        },\n        async createSession(data) {\n            const env_10 = { stack: [], error: void 0, hasError: false };\n            try {\n                const session = to(data);\n                const db = __addDisposableResource(env_10, await getDb(), true);\n                await db.S.insertOne(session);\n                return from(session);\n            }\n            catch (e_10) {\n                env_10.error = e_10;\n                env_10.hasError = true;\n            }\n            finally {\n                const result_10 = __disposeResources(env_10);\n                if (result_10)\n                    await result_10;\n            }\n        },\n        async updateSession(data) {\n            const env_11 = { stack: [], error: void 0, hasError: false };\n            try {\n                const { _id, ...session } = to(data);\n                const db = __addDisposableResource(env_11, await getDb(), true);\n                const updatedSession = await db.S.findOneAndUpdate({ sessionToken: session.sessionToken }, { $set: session }, { returnDocument: \"after\" });\n                return from(updatedSession);\n            }\n            catch (e_11) {\n                env_11.error = e_11;\n                env_11.hasError = true;\n            }\n            finally {\n                const result_11 = __disposeResources(env_11);\n                if (result_11)\n                    await result_11;\n            }\n        },\n        async deleteSession(sessionToken) {\n            const env_12 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_12, await getDb(), true);\n                const session = await db.S.findOneAndDelete({\n                    sessionToken,\n                });\n                return from(session);\n            }\n            catch (e_12) {\n                env_12.error = e_12;\n                env_12.hasError = true;\n            }\n            finally {\n                const result_12 = __disposeResources(env_12);\n                if (result_12)\n                    await result_12;\n            }\n        },\n        async createVerificationToken(data) {\n            const env_13 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_13, await getDb(), true);\n                await db.V.insertOne(to(data));\n                return data;\n            }\n            catch (e_13) {\n                env_13.error = e_13;\n                env_13.hasError = true;\n            }\n            finally {\n                const result_13 = __disposeResources(env_13);\n                if (result_13)\n                    await result_13;\n            }\n        },\n        async useVerificationToken(identifier_token) {\n            const env_14 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_14, await getDb(), true);\n                const verificationToken = await db.V.findOneAndDelete(identifier_token);\n                if (!verificationToken)\n                    return null;\n                const { _id, ...rest } = verificationToken;\n                return rest;\n            }\n            catch (e_14) {\n                env_14.error = e_14;\n                env_14.hasError = true;\n            }\n            finally {\n                const result_14 = __disposeResources(env_14);\n                if (result_14)\n                    await result_14;\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/mongodb-adapter/index.js\n");

/***/ })

};
;