"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-pages-browser)/./node_modules/@fortawesome/react-fontawesome/dist/index.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(app-pages-browser)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LikedSongsCard = (param)=>{\n    let { count = 0, pinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"liked-card\",\n        href: \"/liked\",\n        \"aria-label\": \"Open Liked Songs playlist\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-art\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: \"heart-icon\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-text\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-title\",\n                        children: \"Liked Songs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-meta\",\n                        children: [\n                            pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faThumbtack,\n                                style: {\n                                    color: '#1ed760',\n                                    transform: 'rotate(35deg)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 22\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    marginRight: '4px'\n                                },\n                                children: \"Playlist\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined),\n                            typeof count === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"• \",\n                                    count,\n                                    \" songs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 41\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LikedSongsCard;\nconst Sidebar = (param)=>{\n    let { activeItem = 'home', likedCount = 0, likedPinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo\",\n                children: \"Melodix\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'home' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M12.5 3.247a1 1 0 00-1 0L4 7.577V20h4.5v-6a1 1 0 011-1h5a1 1 0 011 1v6H20V7.577l-7.5-4.33z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'search' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M10.533 1.279c-5.18 0-9.407 4.14-9.407 9.279s4.226 9.279 9.407 9.279c2.234 0 4.29-.77 5.907-2.058l4.353 4.353a1 1 0 101.414-1.414l-4.344-4.344a9.157 9.157 0 002.077-5.816c0-5.14-4.226-9.28-9.407-9.28zm-7.407 9.279c0-4.006 3.302-7.28 7.407-7.28s7.407 3.274 7.407 7.28-3.302 7.279-7.407 7.279-7.407-3.273-7.407-7.28z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'library' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14.5 2.134a1 1 0 011 0l6 3.464a1 1 0 01.5.866V21a1 1 0 01-1 1h-6a1 1 0 01-1-1V3a1 1 0 01.5-.866zM3 22a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1zm6 0a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Your Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divider\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"playlists\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LikedSongsCard, {\n                    count: likedCount,\n                    pinned: likedPinned\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"LikedSongsCard\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});