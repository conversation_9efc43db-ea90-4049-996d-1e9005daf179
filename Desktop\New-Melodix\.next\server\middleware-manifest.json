{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "S3XlBHIdaQfGH2vkeaUO8eUvyKxUuQhTTY/E2/o2i1A=", "__NEXT_PREVIEW_MODE_ID": "b04fb39b04cf317eb60d304dbc80aa22", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d48ec196864a63881096331df08923e6ec6f352181dd197f058c249d623b5e65", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1b137507be188cce9cd34db7e39e4bae30e5380a4e66ad7df28d273ed343334d"}}}, "functions": {}, "sortedMiddleware": ["/"]}