import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Bunny.net S3 configuration
const s3Client = new S3Client({
  region: 'us-east-1', // Default region for Bunny.net
  endpoint: 'https://storage.bunnycdn.com', // Correct endpoint for Bunny.net storage
  credentials: {
    accessKeyId: process.env.BUNNY_STORAGE_ACCESS_KEY!,
    secretAccessKey: process.env.BUNNY_STORAGE_SECRET_KEY!,
  },
  forcePathStyle: true, // Required for Bunny.net
});

export interface UploadOptions {
  fileName: string;
  contentType: string;
  folder: string;
}

export interface FileUrls {
  coverImageUrl: string;
  videoUrl: string;
}

export async function uploadFileToStorage(
  file: Buffer,
  options: UploadOptions
): Promise<string> {
  const key = `${options.folder}/${options.fileName}`;
  
  const command = new PutObjectCommand({
    Bucket: process.env.BUNNY_STORAGE_BUCKET!,
    Key: key,
    Body: file,
    ContentType: options.contentType,
  });

  await s3Client.send(command);
  
  // Return the public URL
  return `https://${process.env.BUNNY_STORAGE_BUCKET}.b-cdn.net/${key}`;
}

export async function generatePresignedUploadUrl(
  fileName: string,
  contentType: string,
  folder: string
): Promise<string> {
  const key = `${folder}/${fileName}`;
  
  const command = new PutObjectCommand({
    Bucket: process.env.BUNNY_STORAGE_BUCKET!,
    Key: key,
    ContentType: contentType,
  });

  return await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour
}

export async function getFileUrl(key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: process.env.BUNNY_STORAGE_BUCKET!,
    Key: key,
  });

  return `https://${process.env.BUNNY_STORAGE_BUCKET}.b-cdn.net/${key}`;
}

export function generateUniqueFileName(originalName: string, prefix: string = ''): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop();
  return `${prefix}${timestamp}-${randomString}.${extension}`;
}