"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/contexts/PlayerContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/PlayerContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerProvider: () => (/* binding */ PlayerProvider),\n/* harmony export */   usePlayer: () => (/* binding */ usePlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ usePlayer,PlayerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PlayerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst usePlayer = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PlayerContext);\n    if (!context) {\n        throw new Error('usePlayer must be used within a PlayerProvider');\n    }\n    return context;\n};\n_s(usePlayer, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst PlayerProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.7);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Keys for local storage\n    const PLAYER_STATE_KEY = 'melodix-player-state';\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fadeIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fade constants (in milliseconds)\n    const FADE_DURATION = 500; // 500ms fade in/out\n    const FADE_STEP = 0.05; // Volume change per step\n    // Load player state from local storage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedState = localStorage.getItem(PLAYER_STATE_KEY);\n                    if (savedState) {\n                        const { song, time, playing, vol } = JSON.parse(savedState);\n                        setCurrentSong(song);\n                        setCurrentTime(time || 0);\n                        setIsPlaying(false); // Always set to false on page reload\n                        setVolume(vol || 0.7);\n                        // Set up the audio with saved position but don't play\n                        if (song) {\n                            // Set a small delay to ensure audioRef is set up\n                            setTimeout({\n                                \"PlayerProvider.useEffect\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.src = song.audioUrl;\n                                        audioRef.current.currentTime = time || 0;\n                                        audioRef.current.volume = vol || 0.7;\n                                    }\n                                }\n                            }[\"PlayerProvider.useEffect\"], 100);\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error loading player state from localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Save player state to local storage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const stateToSave = {\n                        song: currentSong,\n                        time: currentTime,\n                        playing: isPlaying,\n                        vol: volume\n                    };\n                    localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));\n                } catch (error) {\n                    console.error('Error saving player state to localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        currentSong,\n        currentTime,\n        isPlaying,\n        volume\n    ]);\n    // Initialize audio element\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                const handleTimeUpdate = {\n                    \"PlayerProvider.useEffect.handleTimeUpdate\": ()=>{\n                        if (audioRef.current) {\n                            setCurrentTime(audioRef.current.currentTime);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleTimeUpdate\"];\n                const handleLoadedMetadata = {\n                    \"PlayerProvider.useEffect.handleLoadedMetadata\": ()=>{\n                        if (audioRef.current) {\n                            setDuration(audioRef.current.duration);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleLoadedMetadata\"];\n                const handleEnded = {\n                    \"PlayerProvider.useEffect.handleEnded\": ()=>{\n                        setIsPlaying(false);\n                        // Auto-play next song if available\n                        if (playlist.length > 0) {\n                            const currentIndex = currentSong ? playlist.findIndex({\n                                \"PlayerProvider.useEffect.handleEnded\": (song)=>song._id === currentSong._id\n                            }[\"PlayerProvider.useEffect.handleEnded\"]) : -1;\n                            if (currentIndex < playlist.length - 1) {\n                                play(playlist[currentIndex + 1]);\n                            }\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleEnded\"];\n                const handleError = {\n                    \"PlayerProvider.useEffect.handleError\": (e)=>{\n                        console.error('Audio error:', e);\n                        setIsPlaying(false);\n                    }\n                }[\"PlayerProvider.useEffect.handleError\"];\n                if (audioRef.current) {\n                    audioRef.current.addEventListener('timeupdate', handleTimeUpdate);\n                    audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);\n                    audioRef.current.addEventListener('ended', handleEnded);\n                    audioRef.current.addEventListener('error', handleError);\n                    audioRef.current.volume = volume;\n                }\n                return ({\n                    \"PlayerProvider.useEffect\": ()=>{\n                        if (audioRef.current) {\n                            audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);\n                            audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);\n                            audioRef.current.removeEventListener('ended', handleEnded);\n                            audioRef.current.removeEventListener('error', handleError);\n                            audioRef.current.pause();\n                            audioRef.current.src = '';\n                        }\n                    }\n                })[\"PlayerProvider.useEffect\"];\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Update volume when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (audioRef.current) {\n                audioRef.current.volume = volume;\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        volume\n    ]);\n    const play = (song)=>{\n        if (audioRef.current) {\n            audioRef.current.src = song.audioUrl;\n            // Check if we're resuming the same song and restore position\n            if (currentSong && currentSong._id === song._id && currentTime > 0) {\n                audioRef.current.currentTime = currentTime;\n            } else {\n                // Reset position if it's a different song\n                audioRef.current.currentTime = 0;\n            }\n            audioRef.current.play();\n            setCurrentSong(song);\n            setIsPlaying(true);\n        }\n    };\n    const pause = ()=>{\n        if (audioRef.current) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    };\n    const togglePlay = ()=>{\n        if (!currentSong) return;\n        if (isPlaying) {\n            pause();\n        } else {\n            if (audioRef.current) {\n                audioRef.current.src = currentSong.audioUrl;\n                // Restore the saved playback position when resuming\n                if (currentTime > 0) {\n                    audioRef.current.currentTime = currentTime;\n                }\n                audioRef.current.play();\n                setIsPlaying(true);\n            }\n        }\n    };\n    // Add keyboard event listener for space bar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PlayerProvider.useEffect.handleKeyDown\": (e)=>{\n                    // Check if space bar is pressed (keyCode 32 or ' ')\n                    if (e.code === 'Space' || e.keyCode === 32) {\n                        // Prevent default behavior (scrolling) when space is pressed\n                        e.preventDefault();\n                        togglePlay();\n                    }\n                }\n            }[\"PlayerProvider.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Clean up event listener on unmount\n            return ({\n                \"PlayerProvider.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PlayerProvider.useEffect\"];\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        togglePlay\n    ]);\n    const seek = (time)=>{\n        if (audioRef.current) {\n            audioRef.current.currentTime = time;\n            setCurrentTime(time);\n        }\n    };\n    const next = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex < playlist.length - 1) {\n            play(playlist[currentIndex + 1]);\n        }\n    };\n    const previous = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex > 0) {\n            play(playlist[currentIndex - 1]);\n        } else {\n            // If at first song, restart it\n            if (audioRef.current) {\n                audioRef.current.currentTime = 0;\n                audioRef.current.play();\n                setIsPlaying(true);\n            }\n        }\n    };\n    const value = {\n        currentSong,\n        isPlaying,\n        currentTime,\n        duration,\n        volume,\n        play,\n        pause,\n        togglePlay,\n        setVolume,\n        seek,\n        next,\n        previous,\n        playlist,\n        setPlaylist\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\contexts\\\\PlayerContext.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PlayerProvider, \"EcgTwH/jr+6Qhi3udPboeiSbGlY=\");\n_c = PlayerProvider;\nvar _c;\n$RefreshReg$(_c, \"PlayerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contexts/PlayerContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"529d914e2dd9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXERlc2t0b3BcXE5ldy1NZWxvZGl4XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTI5ZDkxNGUyZGQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});