'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faThumbtack } from '@fortawesome/free-solid-svg-icons';

interface SidebarProps {
  activeItem?: string;
  likedCount?: number;
  likedPinned?: boolean;
}

const LikedSongsCard: React.FC<{ count?: number; pinned?: boolean }> = ({
  count = 0,
  pinned = true,
}) => {
  return (
    <a className="liked-card" href="/liked" aria-label="Open Liked Songs playlist">
      <div className="liked-art" aria-hidden="true">
        <svg viewBox="0 0 24 24" className="heart-icon" fill="currentColor">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
        </svg>
      </div>

      <div className="liked-text">
        <div className="liked-title">
          Liked Songs
        </div>
        <div className="liked-meta" style={{ textAlign: 'center', paddingRight: '10px' }}>
          {pinned && <FontAwesomeIcon icon={faThumbtack} style={{ color: '#1ed760', transform: 'rotate(45deg)' }} />}
          <span>Playlist</span>
          {typeof count === 'number' && <span>• {count} songs</span>}
        </div>
      </div>
    </a>
  );
};

const Sidebar: React.FC<SidebarProps> = ({ activeItem = 'home', likedCount = 0, likedPinned = true }) => {
  return (
    <div className="sidebar">
      <div className="logo">Melodix</div>
      
      <nav>
        <a className={`nav-item ${activeItem === 'home' ? 'active' : ''}`}>
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12.5 3.247a1 1 0 00-1 0L4 7.577V20h4.5v-6a1 1 0 011-1h5a1 1 0 011 1v6H20V7.577l-7.5-4.33z"/>
          </svg>
          <span>Home</span>
        </a>
        <a className={`nav-item ${activeItem === 'search' ? 'active' : ''}`}>
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M10.533 1.279c-5.18 0-9.407 4.14-9.407 9.279s4.226 9.279 9.407 9.279c2.234 0 4.29-.77 5.907-2.058l4.353 4.353a1 1 0 101.414-1.414l-4.344-4.344a9.157 9.157 0 002.077-5.816c0-5.14-4.226-9.28-9.407-9.28zm-7.407 9.279c0-4.006 3.302-7.28 7.407-7.28s7.407 3.274 7.407 7.28-3.302 7.279-7.407 7.279-7.407-3.273-7.407-7.28z"/>
          </svg>
          <span>Search</span>
        </a>
        <a className={`nav-item ${activeItem === 'library' ? 'active' : ''}`}>
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M14.5 2.134a1 1 0 011 0l6 3.464a1 1 0 01.5.866V21a1 1 0 01-1 1h-6a1 1 0 01-1-1V3a1 1 0 01.5-.866zM3 22a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1zm6 0a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1z"/>
          </svg>
          <span>Your Library</span>
        </a>
      </nav>

      <div className="divider"></div>

      <div className="playlists">
        <LikedSongsCard count={likedCount} pinned={likedPinned} />
      </div>
    </div>
  );
};

export default Sidebar;