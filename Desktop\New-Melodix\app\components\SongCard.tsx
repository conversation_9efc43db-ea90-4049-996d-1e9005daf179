'use client';

import React from 'react';
import { usePlayer } from '../contexts/PlayerContext';

interface Song {
  _id: string;
  title: string;
  author: string;
  coverImageUrl: string;
  audioUrl: string;
  uploadedBy: {
    username?: string;
    firstName?: string;
    lastName?: string;
  };
  createdAt: string;
}

interface SongCardProps {
  song: Song;
}

const SongCard: React.FC<SongCardProps> = ({ song }) => {
  const { currentSong, isPlaying, play } = usePlayer();

  const handlePlay = () => {
    play(song);
  };

  const isCurrentSong = currentSong?._id === song._id;

  return (
    <div
      className={`bg-gray-800 rounded-lg p-4 hover:bg-gray-700 cursor-pointer transition-colors duration-200 ${
        isCurrentSong ? 'ring-2 ring-green-500' : ''
      }`}
      onClick={handlePlay}
    >
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          <img
            src={song.coverImageUrl}
            alt={song.title}
            className="w-16 h-16 rounded-md object-cover"
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-white font-medium truncate">{song.title}</h3>
          <p className="text-gray-400 text-sm truncate">{song.author}</p>
          {song.uploadedBy?.username && (
            <p className="text-gray-500 text-xs">By {song.uploadedBy.username}</p>
          )}
        </div>
        <div className="flex-shrink-0">
          <button className={`text-green-500 hover:text-green-400 transition-colors ${isCurrentSong && isPlaying ? 'text-green-400' : ''}`}>
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              {isCurrentSong && isPlaying ? (
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              ) : (
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              )}
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SongCard;