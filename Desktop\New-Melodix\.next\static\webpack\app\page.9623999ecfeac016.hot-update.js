"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/contexts/PlayerContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/PlayerContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerProvider: () => (/* binding */ PlayerProvider),\n/* harmony export */   usePlayer: () => (/* binding */ usePlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ usePlayer,PlayerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PlayerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst usePlayer = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PlayerContext);\n    if (!context) {\n        throw new Error('usePlayer must be used within a PlayerProvider');\n    }\n    return context;\n};\n_s(usePlayer, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst PlayerProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.7);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Keys for local storage\n    const PLAYER_STATE_KEY = 'melodix-player-state';\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fadeIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fade constants (in milliseconds)\n    const FADE_DURATION = 500; // 500ms fade in/out\n    const FADE_STEP = 0.05; // Volume change per step\n    // Load player state from local storage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedState = localStorage.getItem(PLAYER_STATE_KEY);\n                    if (savedState) {\n                        const { song, time, playing, vol } = JSON.parse(savedState);\n                        setCurrentSong(song);\n                        setCurrentTime(time || 0);\n                        setIsPlaying(false); // Always set to false on page reload\n                        setVolume(vol || 0.7);\n                        // Set up the audio with saved position but don't play\n                        if (song) {\n                            // Set a small delay to ensure audioRef is set up\n                            setTimeout({\n                                \"PlayerProvider.useEffect\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.src = song.audioUrl;\n                                        audioRef.current.currentTime = time || 0;\n                                        audioRef.current.volume = vol || 0.7;\n                                    }\n                                }\n                            }[\"PlayerProvider.useEffect\"], 100);\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error loading player state from localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Save player state to local storage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const stateToSave = {\n                        song: currentSong,\n                        time: currentTime,\n                        playing: isPlaying,\n                        vol: volume\n                    };\n                    localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));\n                } catch (error) {\n                    console.error('Error saving player state to localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        currentSong,\n        currentTime,\n        isPlaying,\n        volume\n    ]);\n    // Initialize audio element\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                const handleTimeUpdate = {\n                    \"PlayerProvider.useEffect.handleTimeUpdate\": ()=>{\n                        if (audioRef.current) {\n                            setCurrentTime(audioRef.current.currentTime);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleTimeUpdate\"];\n                const handleLoadedMetadata = {\n                    \"PlayerProvider.useEffect.handleLoadedMetadata\": ()=>{\n                        if (audioRef.current) {\n                            setDuration(audioRef.current.duration);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleLoadedMetadata\"];\n                const handleEnded = {\n                    \"PlayerProvider.useEffect.handleEnded\": ()=>{\n                        setIsPlaying(false);\n                        // Auto-play next song if available\n                        if (playlist.length > 0) {\n                            const currentIndex = currentSong ? playlist.findIndex({\n                                \"PlayerProvider.useEffect.handleEnded\": (song)=>song._id === currentSong._id\n                            }[\"PlayerProvider.useEffect.handleEnded\"]) : -1;\n                            if (currentIndex < playlist.length - 1) {\n                                play(playlist[currentIndex + 1]);\n                            }\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleEnded\"];\n                const handleError = {\n                    \"PlayerProvider.useEffect.handleError\": (e)=>{\n                        console.error('Audio error:', e);\n                        setIsPlaying(false);\n                    }\n                }[\"PlayerProvider.useEffect.handleError\"];\n                if (audioRef.current) {\n                    audioRef.current.addEventListener('timeupdate', handleTimeUpdate);\n                    audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);\n                    audioRef.current.addEventListener('ended', handleEnded);\n                    audioRef.current.addEventListener('error', handleError);\n                    audioRef.current.volume = volume;\n                }\n                return ({\n                    \"PlayerProvider.useEffect\": ()=>{\n                        if (audioRef.current) {\n                            audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);\n                            audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);\n                            audioRef.current.removeEventListener('ended', handleEnded);\n                            audioRef.current.removeEventListener('error', handleError);\n                            audioRef.current.pause();\n                            audioRef.current.src = '';\n                        }\n                    }\n                })[\"PlayerProvider.useEffect\"];\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Update volume when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (audioRef.current) {\n                audioRef.current.volume = volume;\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        volume\n    ]);\n    // Fade in effect\n    const fadeIn = ()=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        let currentVolume = 0;\n        const targetVolume = volume;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function for smoother transition\n            currentVolume = targetVolume * progress;\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    // Fade out effect\n    const fadeOut = (callback)=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        const startVolume = audioRef.current ? audioRef.current.volume : 0;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function\n            const currentVolume = startVolume * (1 - progress);\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n                // Execute callback after fade is complete\n                if (callback) {\n                    callback();\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    const play = (song)=>{\n        if (audioRef.current) {\n            audioRef.current.src = song.audioUrl;\n            // Check if we're resuming the same song and restore position\n            if (currentSong && currentSong._id === song._id && currentTime > 0) {\n                audioRef.current.currentTime = currentTime;\n            } else {\n                // Reset position if it's a different song\n                audioRef.current.currentTime = 0;\n            }\n            // Set initial volume to 0 for fade in effect\n            audioRef.current.volume = 0;\n            audioRef.current.play();\n            setCurrentSong(song);\n            setIsPlaying(true);\n            // Start fade in effect\n            fadeIn();\n        }\n    };\n    const pause = ()=>{\n        if (audioRef.current) {\n            // Fade out before pausing\n            fadeOut(()=>{\n                var _audioRef_current;\n                (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.pause();\n                setIsPlaying(false);\n            });\n        }\n    };\n    const togglePlay = ()=>{\n        if (!currentSong) return;\n        if (isPlaying) {\n            pause();\n        } else {\n            if (audioRef.current) {\n                audioRef.current.src = currentSong.audioUrl;\n                // Restore the saved playback position when resuming\n                if (currentTime > 0) {\n                    audioRef.current.currentTime = currentTime;\n                }\n                // Set initial volume to 0 for fade in effect\n                audioRef.current.volume = 0;\n                audioRef.current.play();\n                setIsPlaying(true);\n                // Start fade in effect\n                fadeIn();\n            }\n        }\n    };\n    // Add keyboard event listener for space bar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PlayerProvider.useEffect.handleKeyDown\": (e)=>{\n                    // Check if space bar is pressed (keyCode 32 or ' ')\n                    if (e.code === 'Space' || e.keyCode === 32) {\n                        // Prevent default behavior (scrolling) when space is pressed\n                        e.preventDefault();\n                        togglePlay();\n                    }\n                }\n            }[\"PlayerProvider.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Clean up event listener on unmount\n            return ({\n                \"PlayerProvider.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PlayerProvider.useEffect\"];\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        togglePlay\n    ]);\n    const seek = (time)=>{\n        if (audioRef.current) {\n            audioRef.current.currentTime = time;\n            setCurrentTime(time);\n        }\n    };\n    const next = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex < playlist.length - 1) {\n            // Fade out before switching to next song\n            fadeOut(()=>{\n                play(playlist[currentIndex + 1]);\n            });\n        }\n    };\n    const previous = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex > 0) {\n            // Fade out before switching to previous song\n            fadeOut(()=>{\n                play(playlist[currentIndex - 1]);\n            });\n        } else {\n            // If at first song, restart it with fade in\n            if (audioRef.current) {\n                audioRef.current.currentTime = 0;\n                audioRef.current.volume = 0; // Reset volume for fade in\n                audioRef.current.play();\n                setIsPlaying(true);\n                fadeIn(); // Apply fade in effect\n            }\n        }\n    };\n    const value = {\n        currentSong,\n        isPlaying,\n        currentTime,\n        duration,\n        volume,\n        play,\n        pause,\n        togglePlay,\n        setVolume,\n        seek,\n        next,\n        previous,\n        playlist,\n        setPlaylist\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\contexts\\\\PlayerContext.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PlayerProvider, \"EcgTwH/jr+6Qhi3udPboeiSbGlY=\");\n_c = PlayerProvider;\nvar _c;\n$RefreshReg$(_c, \"PlayerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contexts/PlayerContext.tsx\n"));

/***/ })

});