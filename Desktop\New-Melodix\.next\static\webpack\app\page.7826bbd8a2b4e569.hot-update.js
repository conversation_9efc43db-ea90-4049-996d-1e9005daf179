"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/contexts/PlayerContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/PlayerContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerProvider: () => (/* binding */ PlayerProvider),\n/* harmony export */   usePlayer: () => (/* binding */ usePlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ usePlayer,PlayerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PlayerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst usePlayer = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PlayerContext);\n    if (!context) {\n        throw new Error('usePlayer must be used within a PlayerProvider');\n    }\n    return context;\n};\n_s(usePlayer, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst PlayerProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.7);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Keys for local storage\n    const PLAYER_STATE_KEY = 'melodix-player-state';\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fadeIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fade constants (in milliseconds)\n    const FADE_DURATION = 500; // 500ms fade in/out\n    const FADE_STEP = 0.05; // Volume change per step\n    // Load player state from local storage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedState = localStorage.getItem(PLAYER_STATE_KEY);\n                    if (savedState) {\n                        const { song, time, playing, vol } = JSON.parse(savedState);\n                        setCurrentSong(song);\n                        setCurrentTime(time || 0);\n                        setIsPlaying(false); // Always set to false on page reload\n                        setVolume(vol || 0.7);\n                        // Set up the audio with saved position but don't play\n                        if (song) {\n                            // Set a small delay to ensure audioRef is set up\n                            setTimeout({\n                                \"PlayerProvider.useEffect\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.src = song.audioUrl;\n                                        audioRef.current.currentTime = time || 0;\n                                        audioRef.current.volume = vol || 0.7;\n                                    }\n                                }\n                            }[\"PlayerProvider.useEffect\"], 100);\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error loading player state from localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Save player state to local storage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const stateToSave = {\n                        song: currentSong,\n                        time: currentTime,\n                        playing: isPlaying,\n                        vol: volume\n                    };\n                    localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));\n                } catch (error) {\n                    console.error('Error saving player state to localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        currentSong,\n        currentTime,\n        isPlaying,\n        volume\n    ]);\n    // Initialize audio element\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                const handleTimeUpdate = {\n                    \"PlayerProvider.useEffect.handleTimeUpdate\": ()=>{\n                        if (audioRef.current) {\n                            setCurrentTime(audioRef.current.currentTime);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleTimeUpdate\"];\n                const handleLoadedMetadata = {\n                    \"PlayerProvider.useEffect.handleLoadedMetadata\": ()=>{\n                        if (audioRef.current) {\n                            setDuration(audioRef.current.duration);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleLoadedMetadata\"];\n                const handleEnded = {\n                    \"PlayerProvider.useEffect.handleEnded\": ()=>{\n                        setIsPlaying(false);\n                        // Auto-play next song if available\n                        if (playlist.length > 0) {\n                            const currentIndex = currentSong ? playlist.findIndex({\n                                \"PlayerProvider.useEffect.handleEnded\": (song)=>song._id === currentSong._id\n                            }[\"PlayerProvider.useEffect.handleEnded\"]) : -1;\n                            if (currentIndex < playlist.length - 1) {\n                                play(playlist[currentIndex + 1]);\n                            }\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleEnded\"];\n                const handleError = {\n                    \"PlayerProvider.useEffect.handleError\": (e)=>{\n                        console.error('Audio error:', e);\n                        setIsPlaying(false);\n                    }\n                }[\"PlayerProvider.useEffect.handleError\"];\n                if (audioRef.current) {\n                    audioRef.current.addEventListener('timeupdate', handleTimeUpdate);\n                    audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);\n                    audioRef.current.addEventListener('ended', handleEnded);\n                    audioRef.current.addEventListener('error', handleError);\n                    audioRef.current.volume = volume;\n                }\n                return ({\n                    \"PlayerProvider.useEffect\": ()=>{\n                        if (audioRef.current) {\n                            audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);\n                            audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);\n                            audioRef.current.removeEventListener('ended', handleEnded);\n                            audioRef.current.removeEventListener('error', handleError);\n                            audioRef.current.pause();\n                            audioRef.current.src = '';\n                        }\n                    }\n                })[\"PlayerProvider.useEffect\"];\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Update volume when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (audioRef.current) {\n                audioRef.current.volume = volume;\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        volume\n    ]);\n    // Fade in effect\n    const fadeIn = ()=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        let currentVolume = 0;\n        const targetVolume = volume;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function for smoother transition\n            currentVolume = targetVolume * progress;\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    // Fade out effect\n    const fadeOut = (callback)=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        const startVolume = audioRef.current ? audioRef.current.volume : 0;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function\n            const currentVolume = startVolume * (1 - progress);\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n                // Execute callback after fade is complete\n                if (callback) {\n                    callback();\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    const play = (song)=>{\n        if (audioRef.current) {\n            audioRef.current.src = song.audioUrl;\n            // Check if we're resuming the same song and restore position\n            if (currentSong && currentSong._id === song._id && currentTime > 0) {\n                audioRef.current.currentTime = currentTime;\n            } else {\n                // Reset position if it's a different song\n                audioRef.current.currentTime = 0;\n            }\n            // Set initial volume to 0 for fade in effect\n            audioRef.current.volume = 0;\n            audioRef.current.play();\n            setCurrentSong(song);\n            setIsPlaying(true);\n            // Start fade in effect\n            fadeIn();\n        }\n    };\n    const pause = ()=>{\n        if (audioRef.current) {\n            // Fade out before pausing\n            fadeOut(()=>{\n                var _audioRef_current;\n                (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.pause();\n                setIsPlaying(false);\n            });\n        }\n    };\n    const togglePlay = ()=>{\n        if (!currentSong) return;\n        if (isPlaying) {\n            pause();\n        } else {\n            if (audioRef.current) {\n                audioRef.current.src = currentSong.audioUrl;\n                // Restore the saved playback position when resuming\n                if (currentTime > 0) {\n                    audioRef.current.currentTime = currentTime;\n                }\n                // Set initial volume to 0 for fade in effect\n                audioRef.current.volume = 0;\n                audioRef.current.play();\n                setIsPlaying(true);\n                // Start fade in effect\n                fadeIn();\n            }\n        }\n    };\n    // Add keyboard event listener for space bar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PlayerProvider.useEffect.handleKeyDown\": (e)=>{\n                    // Check if space bar is pressed (keyCode 32 or ' ')\n                    if (e.code === 'Space' || e.keyCode === 32) {\n                        // Prevent default behavior (scrolling) when space is pressed\n                        e.preventDefault();\n                        togglePlay();\n                    }\n                }\n            }[\"PlayerProvider.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Clean up event listener on unmount\n            return ({\n                \"PlayerProvider.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PlayerProvider.useEffect\"];\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        togglePlay\n    ]);\n    const seek = (time)=>{\n        if (audioRef.current) {\n            audioRef.current.currentTime = time;\n            setCurrentTime(time);\n        }\n    };\n    const next = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex < playlist.length - 1) {\n            play(playlist[currentIndex + 1]);\n        }\n    };\n    const previous = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex > 0) {\n            play(playlist[currentIndex - 1]);\n        } else {\n            // If at first song, restart it\n            if (audioRef.current) {\n                audioRef.current.currentTime = 0;\n                audioRef.current.play();\n                setIsPlaying(true);\n            }\n        }\n    };\n    const value = {\n        currentSong,\n        isPlaying,\n        currentTime,\n        duration,\n        volume,\n        play,\n        pause,\n        togglePlay,\n        setVolume,\n        seek,\n        next,\n        previous,\n        playlist,\n        setPlaylist\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\contexts\\\\PlayerContext.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PlayerProvider, \"EcgTwH/jr+6Qhi3udPboeiSbGlY=\");\n_c = PlayerProvider;\nvar _c;\n$RefreshReg$(_c, \"PlayerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contexts/PlayerContext.tsx\n"));

/***/ })

});