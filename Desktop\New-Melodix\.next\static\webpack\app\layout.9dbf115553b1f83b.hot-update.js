"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/contexts/PlayerContext.tsx":
/*!****************************************!*\
  !*** ./app/contexts/PlayerContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerProvider: () => (/* binding */ PlayerProvider),\n/* harmony export */   usePlayer: () => (/* binding */ usePlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ usePlayer,PlayerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PlayerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst usePlayer = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PlayerContext);\n    if (!context) {\n        throw new Error('usePlayer must be used within a PlayerProvider');\n    }\n    return context;\n};\n_s(usePlayer, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst PlayerProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.7);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Keys for local storage\n    const PLAYER_STATE_KEY = 'melodix-player-state';\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fadeIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fade constants (in milliseconds)\n    const FADE_DURATION = 500; // 500ms fade in/out\n    const FADE_STEP = 0.05; // Volume change per step\n    // Load player state from local storage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const savedState = localStorage.getItem(PLAYER_STATE_KEY);\n                    if (savedState) {\n                        const { song, time, playing, vol } = JSON.parse(savedState);\n                        setCurrentSong(song);\n                        setCurrentTime(time || 0);\n                        setIsPlaying(false); // Always set to false on page reload\n                        setVolume(vol || 0.7);\n                        // Set up the audio with saved position but don't play\n                        if (song) {\n                            // Set a small delay to ensure audioRef is set up\n                            setTimeout({\n                                \"PlayerProvider.useEffect\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.src = song.audioUrl;\n                                        audioRef.current.currentTime = time || 0;\n                                        audioRef.current.volume = vol || 0.7;\n                                    }\n                                }\n                            }[\"PlayerProvider.useEffect\"], 100);\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error loading player state from localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Save player state to local storage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                try {\n                    const stateToSave = {\n                        song: currentSong,\n                        time: currentTime,\n                        playing: isPlaying,\n                        vol: volume\n                    };\n                    localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));\n                } catch (error) {\n                    console.error('Error saving player state to localStorage:', error);\n                }\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        currentSong,\n        currentTime,\n        isPlaying,\n        volume\n    ]);\n    // Initialize audio element\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                const handleTimeUpdate = {\n                    \"PlayerProvider.useEffect.handleTimeUpdate\": ()=>{\n                        if (audioRef.current) {\n                            setCurrentTime(audioRef.current.currentTime);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleTimeUpdate\"];\n                const handleLoadedMetadata = {\n                    \"PlayerProvider.useEffect.handleLoadedMetadata\": ()=>{\n                        if (audioRef.current) {\n                            setDuration(audioRef.current.duration);\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleLoadedMetadata\"];\n                const handleEnded = {\n                    \"PlayerProvider.useEffect.handleEnded\": ()=>{\n                        setIsPlaying(false);\n                        // Auto-play next song if available\n                        if (playlist.length > 0) {\n                            const currentIndex = currentSong ? playlist.findIndex({\n                                \"PlayerProvider.useEffect.handleEnded\": (song)=>song._id === currentSong._id\n                            }[\"PlayerProvider.useEffect.handleEnded\"]) : -1;\n                            if (currentIndex < playlist.length - 1) {\n                                play(playlist[currentIndex + 1]);\n                            }\n                        }\n                    }\n                }[\"PlayerProvider.useEffect.handleEnded\"];\n                const handleError = {\n                    \"PlayerProvider.useEffect.handleError\": (e)=>{\n                        console.error('Audio error:', e);\n                        setIsPlaying(false);\n                    }\n                }[\"PlayerProvider.useEffect.handleError\"];\n                if (audioRef.current) {\n                    audioRef.current.addEventListener('timeupdate', handleTimeUpdate);\n                    audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);\n                    audioRef.current.addEventListener('ended', handleEnded);\n                    audioRef.current.addEventListener('error', handleError);\n                    audioRef.current.volume = volume;\n                }\n                return ({\n                    \"PlayerProvider.useEffect\": ()=>{\n                        if (audioRef.current) {\n                            audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);\n                            audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);\n                            audioRef.current.removeEventListener('ended', handleEnded);\n                            audioRef.current.removeEventListener('error', handleError);\n                            audioRef.current.pause();\n                            audioRef.current.src = '';\n                        }\n                        // Clear any ongoing fade effects\n                        if (fadeIntervalRef.current) {\n                            clearInterval(fadeIntervalRef.current);\n                            fadeIntervalRef.current = null;\n                        }\n                    }\n                })[\"PlayerProvider.useEffect\"];\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], []);\n    // Update volume when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            if (audioRef.current) {\n                audioRef.current.volume = volume;\n            }\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        volume\n    ]);\n    // Fade in effect\n    const fadeIn = ()=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        let currentVolume = 0;\n        const targetVolume = volume;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function for smoother transition\n            currentVolume = targetVolume * progress;\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    // Fade out effect\n    const fadeOut = (callback)=>{\n        if (fadeIntervalRef.current) {\n            clearInterval(fadeIntervalRef.current);\n        }\n        const startVolume = audioRef.current ? audioRef.current.volume : 0;\n        const startTime = Date.now();\n        fadeIntervalRef.current = setInterval(()=>{\n            const elapsed = Date.now() - startTime;\n            const progress = Math.min(elapsed / FADE_DURATION, 1);\n            // Calculate current volume with easing function\n            const currentVolume = startVolume * (1 - progress);\n            if (audioRef.current) {\n                audioRef.current.volume = currentVolume;\n            }\n            if (progress >= 1) {\n                if (fadeIntervalRef.current) {\n                    clearInterval(fadeIntervalRef.current);\n                    fadeIntervalRef.current = null;\n                }\n                // Execute callback after fade is complete\n                if (callback) {\n                    callback();\n                }\n            }\n        }, FADE_STEP * 20); // Update every 20ms for smooth transition\n    };\n    const play = (song)=>{\n        if (audioRef.current) {\n            audioRef.current.src = song.audioUrl;\n            // Check if we're resuming the same song and restore position\n            if (currentSong && currentSong._id === song._id && currentTime > 0) {\n                audioRef.current.currentTime = currentTime;\n            } else {\n                // Reset position if it's a different song\n                audioRef.current.currentTime = 0;\n            }\n            // Set initial volume to 0 for fade in effect\n            audioRef.current.volume = 0;\n            audioRef.current.play();\n            setCurrentSong(song);\n            setIsPlaying(true);\n            // Start fade in effect\n            fadeIn();\n        }\n    };\n    const pause = ()=>{\n        if (audioRef.current) {\n            // Fade out before pausing\n            fadeOut(()=>{\n                var _audioRef_current;\n                (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.pause();\n                setIsPlaying(false);\n            });\n        }\n    };\n    const togglePlay = ()=>{\n        if (!currentSong) return;\n        if (isPlaying) {\n            pause();\n        } else {\n            if (audioRef.current) {\n                audioRef.current.src = currentSong.audioUrl;\n                // Restore the saved playback position when resuming\n                if (currentTime > 0) {\n                    audioRef.current.currentTime = currentTime;\n                }\n                // Set initial volume to 0 for fade in effect\n                audioRef.current.volume = 0;\n                audioRef.current.play();\n                setIsPlaying(true);\n                // Start fade in effect\n                fadeIn();\n            }\n        }\n    };\n    // Add keyboard event listener for space bar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlayerProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PlayerProvider.useEffect.handleKeyDown\": (e)=>{\n                    // Check if space bar is pressed (keyCode 32 or ' ')\n                    if (e.code === 'Space' || e.keyCode === 32) {\n                        // Prevent default behavior (scrolling) when space is pressed\n                        e.preventDefault();\n                        togglePlay();\n                    }\n                }\n            }[\"PlayerProvider.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Clean up event listener on unmount\n            return ({\n                \"PlayerProvider.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PlayerProvider.useEffect\"];\n        }\n    }[\"PlayerProvider.useEffect\"], [\n        togglePlay\n    ]);\n    const seek = (time)=>{\n        if (audioRef.current) {\n            audioRef.current.currentTime = time;\n            setCurrentTime(time);\n        }\n    };\n    const next = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex < playlist.length - 1) {\n            // Fade out before switching to next song\n            fadeOut(()=>{\n                play(playlist[currentIndex + 1]);\n            });\n        }\n    };\n    const previous = ()=>{\n        if (!currentSong || playlist.length === 0) return;\n        const currentIndex = playlist.findIndex((song)=>song._id === currentSong._id);\n        if (currentIndex > 0) {\n            // Fade out before switching to previous song\n            fadeOut(()=>{\n                play(playlist[currentIndex - 1]);\n            });\n        } else {\n            // If at first song, restart it with fade in\n            if (audioRef.current) {\n                audioRef.current.currentTime = 0;\n                audioRef.current.volume = 0; // Reset volume for fade in\n                audioRef.current.play();\n                setIsPlaying(true);\n                fadeIn(); // Apply fade in effect\n            }\n        }\n    };\n    const value = {\n        currentSong,\n        isPlaying,\n        currentTime,\n        duration,\n        volume,\n        play,\n        pause,\n        togglePlay,\n        setVolume,\n        seek,\n        next,\n        previous,\n        playlist,\n        setPlaylist\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\contexts\\\\PlayerContext.tsx\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PlayerProvider, \"EcgTwH/jr+6Qhi3udPboeiSbGlY=\");\n_c = PlayerProvider;\nvar _c;\n$RefreshReg$(_c, \"PlayerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contexts/PlayerContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7fe677b82740\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWlja2FcXERlc2t0b3BcXE5ldy1NZWxvZGl4XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2ZlNjc3YjgyNzQwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});