"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n// ...inside your Sidebar component, near the bottom\n\nconst likedCount = 718; // TODO: wire this up to your data if/when available\n/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n    className: \"playlists\",\n    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"/collection/tracks\",\n        className: \"playlist-item liked-songs \".concat(activeItem === 'liked' ? 'active' : ''),\n        \"aria-label\": \"Open Liked Songs playlist with \".concat(likedCount, \" songs\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-cover\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"heart\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 6.01 4.01 4 6.5 4c1.74 0 3.41 1.01 4.25 2.5C11.59 5.01 13.26 4 15 4 17.49 4 19.5 6.01 19.5 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-meta\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-title\",\n                        children: \"Liked Songs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-subtitle\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"pinned\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"pin\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        \"aria-hidden\": \"true\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M14 2a1 1 0 011 1v4.17l3.1 1.55a1 1 0 01.45 1.39l-3.35 5.81.25 3.73a1 1 0 01-1.64.88l-3-2.2-3 2.2a1 1 0 01-1.64-.88l.25-3.73L5.45 10.1a1 1 0 01.45-1.39L9 7.17V3a1 1 0 011-1h4z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Pinned\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"dot\",\n                                \"aria-hidden\": \"true\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Playlist • \",\n                                    likedCount,\n                                    \" songs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"play-btn\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8 5v14l11-7z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 31,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined)\n}, void 0, false, {\n    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n    lineNumber: 5,\n    columnNumber: 1\n}, undefined);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});