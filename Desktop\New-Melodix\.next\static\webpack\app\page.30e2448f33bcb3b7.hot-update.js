"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/HomePage.tsx":
/*!*************************************!*\
  !*** ./app/components/HomePage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./app/components/Sidebar.tsx\");\n/* harmony import */ var _TopBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopBar */ \"(app-pages-browser)/./app/components/TopBar.tsx\");\n/* harmony import */ var _QuickPlayCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuickPlayCard */ \"(app-pages-browser)/./app/components/QuickPlayCard.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Card */ \"(app-pages-browser)/./app/components/Card.tsx\");\n/* harmony import */ var _SongCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SongCard */ \"(app-pages-browser)/./app/components/SongCard.tsx\");\n/* harmony import */ var _Player__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Player */ \"(app-pages-browser)/./app/components/Player.tsx\");\n/* harmony import */ var _layout_Providers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./layout/Providers */ \"(app-pages-browser)/./app/components/layout/Providers.tsx\");\n/* harmony import */ var _contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../contexts/PlayerContext */ \"(app-pages-browser)/./app/contexts/PlayerContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const [greeting, setGreeting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Good evening');\n    const [songs, setSongs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setPlaylist } = (0,_contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__.usePlayer)();\n    // Update greeting based on time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const updateGreeting = {\n                \"HomePage.useEffect.updateGreeting\": ()=>{\n                    const hour = new Date().getHours();\n                    if (hour < 12) {\n                        setGreeting('Good morning');\n                    } else if (hour < 18) {\n                        setGreeting('Good afternoon');\n                    } else {\n                        setGreeting('Good evening');\n                    }\n                }\n            }[\"HomePage.useEffect.updateGreeting\"];\n            updateGreeting();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Fetch songs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchSongs = {\n                \"HomePage.useEffect.fetchSongs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/songs');\n                        const data = await response.json();\n                        if (response.ok) {\n                            setSongs(data.songs);\n                            setPlaylist(data.songs);\n                        } else {\n                            console.error('Failed to fetch songs:', data.error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching songs:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchSongs\"];\n            fetchSongs();\n        }\n    }[\"HomePage.useEffect\"], [\n        setPlaylist\n    ]);\n    const quickPlayData = [\n        {\n            title: 'Liked Songs',\n            color: '#e91e63'\n        },\n        {\n            title: 'Chill Vibes',\n            color: '#9c27b0'\n        },\n        {\n            title: 'Workout Mix',\n            color: '#f44336'\n        },\n        {\n            title: 'Focus Flow',\n            color: '#009688'\n        },\n        {\n            title: 'Party Hits',\n            color: '#ff9800'\n        },\n        {\n            title: 'Road Trip',\n            color: '#3f51b5'\n        }\n    ];\n    const madeForYouData = [\n        {\n            title: 'Daily Mix 1',\n            description: 'The Weeknd, Drake, Travis Scott and more',\n            color: '#4caf50'\n        },\n        {\n            title: 'Discover Weekly',\n            description: 'Your weekly mixtape of fresh music',\n            color: '#2196f3'\n        },\n        {\n            title: 'Release Radar',\n            description: 'Catch all the latest music from artists you follow',\n            color: '#ff5722'\n        },\n        {\n            title: 'Repeat Rewind',\n            description: 'Songs you can\\'t stop playing',\n            color: '#673ab7'\n        },\n        {\n            title: 'Summer Hits',\n            description: 'The hottest tracks for sunny days',\n            color: '#795548'\n        },\n        {\n            title: 'Acoustic Favorites',\n            description: 'Stripped down versions of your favorites',\n            color: '#607d8b'\n        }\n    ];\n    const popularPlaylistsData = [\n        {\n            title: 'Today\\'s Top Hits',\n            description: 'Ed Sheeran is on top of the Hottest 50!',\n            color: '#e91e63'\n        },\n        {\n            title: 'RapCaviar',\n            description: 'New music from Kendrick Lamar, Travis Scott and more',\n            color: '#009688'\n        },\n        {\n            title: 'Rock Classics',\n            description: 'Rock legends & epic songs',\n            color: '#ff9800'\n        },\n        {\n            title: 'Jazz Vibes',\n            description: 'The original chill instrumental music',\n            color: '#3f51b5'\n        },\n        {\n            title: 'Mint',\n            description: 'The best in electronic music',\n            color: '#f44336'\n        },\n        {\n            title: 'Pop Rising',\n            description: 'Rising pop stars and fresh favorites',\n            color: '#9c27b0'\n        }\n    ];\n    const handlePlayTrack = ()=>{\n        // This would be handled by a music player context/state in a real app\n        console.log('Playing track');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_Providers__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    activeItem: \"home\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"main-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"content-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"greeting\",\n                                    children: greeting\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"quick-play-grid\",\n                                    children: quickPlayData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickPlayCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            title: item.title,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Made for you\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cards-grid\",\n                                    children: madeForYouData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            title: item.title,\n                                            description: item.description,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Recently added songs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"songs-list\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-400\",\n                                        children: \"Loading songs...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined) : songs.length > 0 ? songs.map((song)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SongCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            song: song\n                                        }, song._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-400\",\n                                        children: \"No songs available yet. Check back later!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"Popular playlists\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cards-grid\",\n                                    children: popularPlaylistsData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            title: item.title,\n                                            description: item.description,\n                                            color: item.color,\n                                            onClick: handlePlayTrack\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Player__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\HomePage.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"mklH3NdCPY05ZP6/ZkNybwhxzmk=\", false, function() {\n    return [\n        _contexts_PlayerContext__WEBPACK_IMPORTED_MODULE_9__.usePlayer\n    ];\n});\n_c = HomePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/HomePage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Sidebar = (param)=>{\n    let { activeItem = 'home' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo\",\n                children: \"Melodix\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'home' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M12.5 3.247a1 1 0 00-1 0L4 7.577V20h4.5v-6a1 1 0 011-1h5a1 1 0 011 1v6H20V7.577l-7.5-4.33z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'search' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M10.533 1.279c-5.18 0-9.407 4.14-9.407 9.279s4.226 9.279 9.407 9.279c2.234 0 4.29-.77 5.907-2.058l4.353 4.353a1 1 0 101.414-1.414l-4.344-4.344a9.157 9.157 0 002.077-5.816c0-5.14-4.226-9.28-9.407-9.28zm-7.407 9.279c0-4.006 3.302-7.28 7.407-7.28s7.407 3.274 7.407 7.28-3.302 7.279-7.407 7.279-7.407-3.273-7.407-7.28z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'library' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14.5 2.134a1 1 0 011 0l6 3.464a1 1 0 01.5.866V21a1 1 0 01-1 1h-6a1 1 0 01-1-1V3a1 1 0 01.5-.866zM3 22a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1zm6 0a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Your Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divider\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"playlists\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"playlist-item\",\n                    children: \"Liked Songs\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});