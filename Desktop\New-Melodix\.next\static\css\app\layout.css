/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/4cf2300e9c8272f7-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_188709 {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_188709 {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.16 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-300: oklch(87.1% 0.15 154.449);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-medium: 500;
    --font-weight-bold: 700;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --animate-spin: spin 1s linear infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip-path: inset(50%);
    white-space: nowrap;
    border-width: 0;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-10 {
    margin-left: calc(var(--spacing) * 10);
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .inline-flex {
    display: inline-flex;
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-full {
    width: 100%;
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-transparent {
    border-color: transparent;
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-black {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .object-cover {
    object-fit: cover;
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }
  .text-center {
    text-align: center;
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-white {
    color: var(--color-white);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-green-500 {
    --tw-ring-color: var(--color-green-500);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .focus-within\:ring-2 {
    &:focus-within {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-within\:ring-green-500 {
    &:focus-within {
      --tw-ring-color: var(--color-green-500);
    }
  }
  .focus-within\:ring-offset-2 {
    &:focus-within {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-within\:outline-none {
    &:focus-within {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .hover\:border-gray-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-500);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:text-green-300 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-300);
      }
    }
  }
  .hover\:text-green-400 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-400);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-green-500 {
    &:focus {
      --tw-ring-color: var(--color-green-500);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: #000;
  color: #fff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100vw;
}
.container {
  display: flex;
  height: 100vh;
  width: 100vw;
  max-width: 100vw;
  overflow: hidden;
}
.sidebar {
  width: 240px;
  background-color: #000;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.logo {
  font-size: 28px;
  font-weight: bold;
  padding: 0 12px;
  margin-bottom: 10px;
  color: #1ed760;
}
.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  color: #b3b3b3;
  text-decoration: none;
  border-radius: 4px;
  transition: color 0.2s;
  cursor: pointer;
}
.nav-item:hover {
  color: #fff;
}
.nav-item.active {
  color: #fff;
}
.nav-item svg {
  width: 24px;
  height: 24px;
}
.divider {
  height: 1px;
  background-color: #282828;
  margin: 8px 0;
}
.playlist-item {
  padding: 8px 12px;
  color: #b3b3b3;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
}
.playlist-item:hover {
  color: #fff;
}
.main-content {
  flex: 1;
  background-color: #121212;
  overflow-y: auto;
  padding-bottom: 100px;
  width: 100%;
  min-width: 0;
}
.top-bar {
  background-color: #121212;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}
.nav-buttons {
  display: flex;
  gap: 16px;
}
.nav-btn {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}
.nav-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}
.user-profile {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 16px 4px 4px;
  border-radius: 23px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.user-profile:hover {
  background-color: rgba(0, 0, 0, 0.9);
}
.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}
.search-bar-container {
  flex: 1;
  max-width: 600px;
  margin: 0 32px;
  position: relative;
}
.search-bar-container > div {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: background-color 0.2s;
  width: 100%;
}
.search-bar-container > div:hover {
  background-color: rgba(255, 255, 255, 0.15);
}
.search-bar-container .search-icon {
  color: #b3b3b3;
  flex-shrink: 0;
}
.search-bar-container .search-placeholder {
  color: #b3b3b3;
  font-size: 14px;
  font-weight: 500;
}
.content-section {
  padding: 16px 32px;
}
.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
}
.greeting {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
}
.quick-play-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}
.quick-play-card {
  background-color: #181818;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 16px;
  height: 80px;
  overflow: hidden;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}
.quick-play-card:hover {
  background-color: #282828;
}
.quick-play-card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}
.quick-play-card img {
  width: 80px;
  height: 80px;
  object-fit: cover;
}
.quick-play-card span {
  font-weight: 600;
  font-size: 16px;
}
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}
.card {
  background-color: #181818;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}
.card:hover {
  background-color: #282828;
}
.card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}
.card-image {
  width: 100%;
  aspect-ratio: 1;
  background-color: #333;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}
.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.play-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 48px;
  height: 48px;
  background-color: #1ed760;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transform: translateY(8px);
  transition: all 0.3s;
  box-shadow: 0 8px 8px rgba(0, 0, 0, 0.3);
}
.play-btn:hover {
  transform: scale(1.05) translateY(0);
  background-color: #1fdf64;
}
.play-btn svg {
  width: 20px;
  height: 20px;
  fill: #000;
  margin-left: 2px;
}
.card-title {
  font-weight: 600;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.card-description {
  font-size: 14px;
  color: #b3b3b3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.songs-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 40px;
}
.player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #181818;
  border-top: 1px solid #282828;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}
.player-left {
  display: flex;
  align-items: center;
  gap: 14px;
  min-width: 180px;
  width: 30%;
}
.player-left img {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  background-color: #333;
}
.song-info h4 {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
}
.song-info p {
  font-size: 12px;
  color: #b3b3b3;
}
.heart-icon {
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
}
.heart-icon:hover {
  color: #fff;
}
.player-center {
  flex: 1;
  max-width: 722px;
}
.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
}
.control-btn {
  background: none;
  border: none;
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  display: flex;
  align-items: center;
}
.control-btn:hover {
  color: #fff;
}
.control-btn.play {
  background-color: #fff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.control-btn.play:hover {
  transform: scale(1.06);
}
.progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}
.time {
  font-size: 12px;
  color: #b3b3b3;
  min-width: 40px;
}
.progress {
  flex: 1;
  height: 4px;
  background-color: #4d4d4d;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}
.progress:hover .progress-fill::after,
.progress.dragging .progress-fill::after {
  opacity: 1;
}
.progress-fill {
  height: 100%;
  background-color: #fff;
  border-radius: 2px;
  width: 30%;
  position: relative;
}
.progress-fill::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s;
}
.player-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  width: 30%;
  justify-content: flex-end;
}
.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 125px;
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.user-name {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
}
.sign-out-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-top: 2px;
  text-align: left;
}
.login-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1ed760 0%, #1db954 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
.auth-card {
  background: #000;
  border-radius: 8px;
  padding: 48px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 60px rgba(0, 0, 0, 0.5);
}
.auth-logo {
  text-align: center;
  margin-bottom: 48px;
}
.auth-logo h1 {
  color: #1ed760;
  font-size: 32px;
  font-weight: bold;
  margin: 0;
}
.auth-title {
  color: #fff;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32px;
}
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.form-row {
  display: flex;
  gap: 16px;
}
.form-row .form-group {
  flex: 1;
  width: 100%;
}
.form-row .form-input {
  width: 100%;
}
.form-label {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}
.form-input {
  background: #121212;
  border: 1px solid #727272;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  padding: 12px 16px;
  transition: border-color 0.2s;
}
.form-input:focus {
  outline: none;
  border-color: #1ed760;
}
.form-input::placeholder {
  color: #b3b3b3;
}
.form-error {
  color: #e22134;
  font-size: 12px;
  margin-top: 4px;
}
.auth-button {
  background: #1ed760;
  border: none;
  border-radius: 50px;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  padding: 16px 32px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 16px;
}
.auth-button:hover {
  background: #1fdf64;
  transform: scale(1.04);
}
.auth-button:disabled {
  background: #535353;
  color: #b3b3b3;
  cursor: not-allowed;
  transform: none;
}
.auth-divider {
  display: flex;
  align-items: center;
  margin: 32px 0;
  color: #b3b3b3;
  font-size: 14px;
}
.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #727272;
}
.auth-divider::before {
  margin-right: 16px;
}
.auth-divider::after {
  margin-left: 16px;
}
.auth-link {
  text-align: center;
  margin-top: 32px;
  color: #b3b3b3;
  font-size: 14px;
}
.auth-link a {
  color: #1ed760;
  text-decoration: none;
  font-weight: 600;
}
.auth-link a:hover {
  text-decoration: underline;
}
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #000;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }
  .nav-item span, .playlist-item, .logo {
    display: none;
  }
  .logo::first-letter {
    display: block;
  }
  .auth-card {
    padding: 32px 24px;
  }
  .auth-title {
    font-size: 28px;
  }
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
}
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: #0a0a0a;
}
.animated-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
}
.gradient-sphere {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.5;
  animation: float 20s infinite ease-in-out;
}
.sphere-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #1ed760, #1db954);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}
.sphere-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  bottom: -100px;
  right: -100px;
  animation-delay: 5s;
}
.sphere-3 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 10s;
}
@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}
.login-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
}
.login-branding {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.brand-content {
  max-width: 400px;
}
.brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}
.logo-icon {
  width: 60px;
  height: 60px;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
.brand-name {
  font-size: 48px;
  font-weight: bold;
  background: linear-gradient(135deg, #1ed760, #1db954);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}
.brand-tagline {
  font-size: 20px;
  color: #b3b3b3;
  margin-bottom: 48px;
}
.brand-features {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #fff;
  font-size: 16px;
}
.feature-icon {
  font-size: 24px;
}
.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}
.login-form-wrapper {
  width: 100%;
  max-width: 420px;
}
.form-header {
  margin-bottom: 40px;
}
.form-header h2 {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin: 0 0 8px 0;
}
.form-header p {
  font-size: 16px;
  color: #b3b3b3;
  margin: 0;
}
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.name-fields-row {
  display: flex;
  gap: 16px;
}
.name-fields-row .input-group {
  flex: 1;
  width: 100%;
}
.input-group {
  position: relative;
  transition: all 0.3s;
}
.modern-input {
  width: 100%;
  padding: 20px 48px 8px 48px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fff;
  font-size: 16px;
  transition: all 0.3s;
}
.modern-input:focus {
  outline: none;
  border-color: #1ed760;
  background: rgba(255, 255, 255, 0.08);
}
.modern-label {
  position: absolute;
  left: 48px;
  top: 50%;
  transform: translateY(-50%);
  color: #b3b3b3;
  font-size: 16px;
  pointer-events: none;
  transition: all 0.3s;
}
.input-group.focused .modern-label,
.input-group.filled .modern-label {
  top: 12px;
  font-size: 12px;
  color: #1ed760;
}
.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #b3b3b3;
  transition: color 0.3s;
}
.input-group.focused .input-icon {
  color: #1ed760;
}
.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #b3b3b3;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s;
}
.password-toggle:hover {
  color: #fff;
}
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}
.checkbox-container input {
  display: none;
}
.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #b3b3b3;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
}
.checkbox-container input:checked + .checkbox-custom {
  background: #1ed760;
  border-color: #1ed760;
}
.checkbox-container input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #000;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.checkbox-label {
  color: #b3b3b3;
  font-size: 14px;
}
.forgot-link {
  color: #1ed760;
  font-size: 14px;
  text-decoration: none;
  transition: opacity 0.3s;
}
.forgot-link:hover {
  opacity: 0.8;
}
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(226, 33, 52, 0.1);
  border: 1px solid rgba(226, 33, 52, 0.3);
  border-radius: 8px;
  color: #ff4458;
  font-size: 14px;
}
.submit-button {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #1ed760, #1db954);
  border: none;
  border-radius: 50px;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}
.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}
.submit-button:hover::before {
  left: 100%;
}
.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 215, 96, 0.3);
}
.submit-button:disabled {
  background: #535353;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.button-loader {
  display: flex;
  gap: 4px;
}
.loader-dot {
  width: 8px;
  height: 8px;
  background: #000;
  border-radius: 50%;
  animation: loader-bounce 1.4s infinite ease-in-out both;
}
.loader-dot:nth-child(1) {
  animation-delay: -0.32s;
}
.loader-dot:nth-child(2) {
  animation-delay: -0.16s;
}
@keyframes loader-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
.social-divider {
  text-align: center;
  margin: 32px 0 24px;
  position: relative;
}
.social-divider span {
  background: #0a0a0a;
  padding: 0 16px;
  color: #b3b3b3;
  font-size: 14px;
  position: relative;
  z-index: 1;
}
.social-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}
.social-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}
.social-btn {
  flex: 1;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.social-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}
.signup-link {
  text-align: center;
  color: #b3b3b3;
  font-size: 14px;
}
.signup-link a {
  color: #1ed760;
  text-decoration: none;
  font-weight: 600;
  margin-left: 4px;
  transition: opacity 0.3s;
}
.signup-link a:hover {
  opacity: 0.8;
}
.shake-animation {
  animation: shake 0.5s;
}
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}
@media (max-width: 968px) {
  .login-branding {
    display: none;
  }
  .login-form-container {
    flex: 1;
  }
}
@media (max-width: 480px) {
  .login-form-wrapper {
    padding: 0 20px;
  }
  .social-buttons {
    flex-direction: column;
  }
  .form-header h2 {
    font-size: 28px;
  }
  .name-fields-row {
    flex-direction: column;
    gap: 16px;
  }
}
.user-profile-container {
  position: relative;
}
.user-profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 4px 12px 4px 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.user-profile-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}
.avatar-wrapper {
  position: relative;
}
.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  user-select: none;
}
.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: #22c55e;
  border: 2px solid #121212;
  border-radius: 50%;
}
.chevron-icon {
  transition: transform 0.2s ease;
  opacity: 0.6;
}
.chevron-icon.rotate {
  transform: rotate(180deg);
}
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 240px;
  background: #1e1e1e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: slideDown 0.2s ease;
  overflow: hidden;
}
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.dropdown-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
}
.dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}
.dropdown-user-info {
  flex: 1;
  min-width: 0;
}
.dropdown-user-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropdown-user-email {
  font-size: 12px;
  color: #9ca3af;
  margin: 2px 0 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0;
}
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #e5e7eb;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}
.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}
.dropdown-item svg {
  opacity: 0.7;
  flex-shrink: 0;
}
.dropdown-item:hover svg {
  opacity: 1;
}
.dropdown-item-danger {
  color: #ef4444;
}
.dropdown-item-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #f87171;
}
.login-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}
.login-button:hover {
  background: #16a34a;
  transform: scale(1.02);
}
.skeleton-loader {
  display: flex;
  align-items: center;
  gap: 8px;
}
.skeleton-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
:root {
  --melodix-accent: #1db954;
  --melodix-text: #e7e7e7;
  --melodix-muted: #a1a1aa;
  --melodix-hover: rgba(255, 255, 255, 0.06);
  --melodix-focus: 0 0 0 2px rgba(29, 185, 84, 0.5);
}
.liked-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem 0.6rem;
  border-radius: 10px;
  color: var(--melodix-text);
  text-decoration: none;
  cursor: pointer;
  transition: background 120ms ease, transform 120ms ease;
}
.liked-card:hover {
  background: var(--melodix-hover);
}
.liked-card:active {
  transform: translateY(1px);
}
.liked-card:focus-visible {
  outline: none;
  box-shadow: var(--melodix-focus);
  background: var(--melodix-hover);
}
.liked-art {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: grid;
  place-items: center;
  color: #fff;
  background: linear-gradient(135deg, #6d28d9 0%, #22d3ee 55%, #9333ea 100%);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.35), inset 0 -10px 20px rgba(255, 255, 255, 0.06);
  overflow: hidden;
}
@media (prefers-reduced-motion: no-preference) {
  .liked-art::before {
    content: "";
    position: absolute;
    inset: -40%;
    background: conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0.08), transparent 60%);
    animation: liked-spin 8s linear infinite;
    pointer-events: none;
  }
}
@keyframes liked-spin {
  to {
    transform: rotate(1turn);
  }
}
.heart-icon {
  width: 18px;
  height: 18px;
  opacity: 0.95;
  z-index: 1;
}
.liked-text {
  display: grid;
}
.liked-title {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: var(--melodix-accent);
  letter-spacing: 0.1px;
}
.pin-icon {
  width: 12px;
  height: 12px;
  color: var(--melodix-accent);
  opacity: 0.9;
}
.liked-meta {
  margin-top: 2px;
  font-size: 12.5px;
  line-height: 1.2;
  color: var(--melodix-muted);
  display: flex;
  align-items: center;
  gap: 0.35rem;
}
.playlists {
  display: grid;
  gap: 0.25rem;
}
.search-bar-container {
  display: flex;
  align-items: center;
  background-color: #121212;
  border-radius: 20px;
  padding: 8px 16px;
  height: 40px;
  width: 100%;
  max-width: 400px;
  color: #b3b3b3;
  font-size: 14px;
  font-weight: 500;
}
.search-icon {
  margin-right: 8px;
  color: #b3b3b3;
}
.search-placeholder {
  user-select: none;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-duration: initial;
    }
  }
}
