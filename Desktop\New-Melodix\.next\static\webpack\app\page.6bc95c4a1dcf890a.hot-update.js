"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst LikedSongsCard = (param)=>{\n    let { count = 0, pinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"liked-card\",\n        href: \"/liked\",\n        \"aria-label\": \"Open Liked Songs playlist\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-art\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: \"heart-icon\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"liked-text\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-title\",\n                        children: [\n                            \"Liked Songs\",\n                            pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"pin-icon\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                \"aria-label\": \"Pinned\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14.7 2.3a1 1 0 011.4 1.4l-1.8 1.8 3.3 3.3 1.8-1.8a1 1 0 011.4 1.4l-6.6 6.6V20l-4-1 6.5-6.5-3.3-3.3-6.5 6.5-1-4 6.6-6.6z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"liked-meta\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Playlist\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            typeof count === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"• \",\n                                    count,\n                                    \" songs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 41\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LikedSongsCard;\nconst Sidebar = (param)=>{\n    let { activeItem = 'home', likedCount = 718, likedPinned = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo\",\n                children: \"Melodix\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'home' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M12.5 3.247a1 1 0 00-1 0L4 7.577V20h4.5v-6a1 1 0 011-1h5a1 1 0 011 1v6H20V7.577l-7.5-4.33z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'search' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M10.533 1.279c-5.18 0-9.407 4.14-9.407 9.279s4.226 9.279 9.407 9.279c2.234 0 4.29-.77 5.907-2.058l4.353 4.353a1 1 0 101.414-1.414l-4.344-4.344a9.157 9.157 0 002.077-5.816c0-5.14-4.226-9.28-9.407-9.28zm-7.407 9.279c0-4.006 3.302-7.28 7.407-7.28s7.407 3.274 7.407 7.28-3.302 7.279-7.407 7.279-7.407-3.273-7.407-7.28z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"nav-item \".concat(activeItem === 'library' ? 'active' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14.5 2.134a1 1 0 011 0l6 3.464a1 1 0 01.5.866V21a1 1 0 01-1 1h-6a1 1 0 01-1-1V3a1 1 0 01.5-.866zM3 22a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1zm6 0a1 1 0 01-1-1V3a1 1 0 012 0v18a1 1 0 01-1 1z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Your Library\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divider\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"playlists\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LikedSongsCard, {\n                    count: likedCount,\n                    pinned: likedPinned\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New-Melodix\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"LikedSongsCard\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});